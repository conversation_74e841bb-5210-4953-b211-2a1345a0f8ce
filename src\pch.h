#pragma once

// =============================================================================
// PRECOMPILED HEADER - Core system includes
// =============================================================================

// Fix Winsock conflicts by defining WIN32_LEAN_AND_MEAN and including winsock2.h first
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

// Network Headers (MUST come before Windows.h to prevent conflicts)
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iphlpapi.h>

// Windows API Headers
#include <Windows.h>
#include <TlHelp32.h>
#include <dwmapi.h>
#include <winternl.h>
#include <tchar.h>
#include <mmsystem.h>
#include <shlobj.h>
#include <psapi.h>

// DirectX Headers
#include <d3d9.h>
#include <d3d11.h>
#include <dxgi.h>
#pragma comment(lib,"d3d9.lib")

// Math Headers
#include <corecrt_math_defines.h>
#include <cmath>

// C++ Standard Library - Core
#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <stdexcept>
#include <cstdint>
#include <cstdio>
#include <cstring>

// C++ Standard Library - Containers
#include <unordered_map>
#include <map>
#include <set>
#include <array>
#include <deque>
#include <list>

// C++ Standard Library - Threading
#include <mutex>
#include <atomic>
#include <thread>
#include <chrono>

// C++ Standard Library - I/O
#include <fstream>
#include <sstream>
#include <filesystem>

// C++ Standard Library - Algorithms
#include <algorithm>
#include <functional>
#include <random>
#include <numeric>

// C++ Standard Library - Modern Features
#include <print>
#include <format>

// C++ Standard Library - Utilities
#include <utility>
#include <tuple>
#include <optional>
#include <variant>

// =============================================================================
// EXTERNAL LIBRARIES
// =============================================================================

// ImGui - Immediate Mode GUI Library
#include "../imgui/imgui.h"
#include "../imgui/imgui_impl_dx9.h"
#include "../imgui/imgui_impl_win32.h"
#include "../imgui/imgui_internal.h"
#include "../imgui/misc/freetype/imgui_freetype.h"

// Math and Intrinsics
#include <intrin.h>
#include <immintrin.h>
