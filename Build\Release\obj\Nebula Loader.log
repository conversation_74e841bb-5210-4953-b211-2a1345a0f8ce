﻿  PORTABLE SECURITY BUILD SYSTEM
  ==============================
  Generating new BUILD_SIGNATURE_LEGACY: 0xB84BE659
  Note: Portable system uses runtime entropy, this is just for fallback
  Successfully updated BUILD_SIGNATURE_LEGACY to: 0xB84BE659
  
  Build signature update completed.
  pch.cpp
  imgui_stdlib.cpp
  imgui_freetype.cpp
C:\Users\<USER>\Desktop\coding\nebula\Nebula loader\external\imgui\misc\freetype\imgui_freetype.cpp(40,10): error C1083: Cannot open include file: 'ft2build.h': No such file or directory
  (compiling source file '/external/imgui/misc/freetype/imgui_freetype.cpp')
  
  debug.cpp
  config.cpp
  security.cpp
  core.cpp
  indirect_crash.cpp
  ui.cpp
  imgui.cpp
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_impl_dx9.cpp
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  Main.cpp
