#include <iostream>
#include <string>
#include <cctype>

// Mock skCrypt for testing
struct MockSkCrypt {
    std::string str;
    MockSkCrypt(const char* s) : str(s) {}
    std::string decrypt() { return str; }
};
#define skCrypt(x) MockSkCrypt(x)

// Password validation with comprehensive security rules
struct PasswordValidationResult {
    bool is_valid;
    std::string error_message;
};

PasswordValidationResult validatePassword(const std::string& password) {
    PasswordValidationResult result;
    result.is_valid = false;
    result.error_message = "";

    // Check minimum length (8 characters)
    if (password.length() < 8) {
        result.error_message = skCrypt("Password must be at least 8 characters long").decrypt();
        return result;
    }

    // Check maximum length (12 characters)
    if (password.length() > 12) {
        result.error_message = skCrypt("Password must not exceed 12 characters").decrypt();
        return result;
    }

    // Define forbidden characters that should not be allowed in passwords
    const std::string forbidden_chars = skCrypt("\"'`\\|<>{}[]").decrypt();
    for (char c : password) {
        if (forbidden_chars.find(c) != std::string::npos) {
            result.error_message = skCrypt("Password contains forbidden characters").decrypt();
            return result;
        }
    }

    // Check for at least one special character
    const std::string special_chars = skCrypt("!@#$%^&*()_+-=~.,;:?/").decrypt();
    bool has_special = false;
    for (char c : password) {
        if (special_chars.find(c) != std::string::npos) {
            has_special = true;
            break;
        }
    }

    if (!has_special) {
        result.error_message = skCrypt("Password must contain at least 1 special character").decrypt();
        return result;
    }

    // Additional security checks
    // Check for at least one letter
    bool has_letter = false;
    for (char c : password) {
        if (std::isalpha(c)) {
            has_letter = true;
            break;
        }
    }

    if (!has_letter) {
        result.error_message = skCrypt("Password must contain at least 1 letter").decrypt();
        return result;
    }

    // Check for at least one number
    bool has_number = false;
    for (char c : password) {
        if (std::isdigit(c)) {
            has_number = true;
            break;
        }
    }

    if (!has_number) {
        result.error_message = skCrypt("Password must contain at least 1 number").decrypt();
        return result;
    }

    // All validation checks passed
    result.is_valid = true;
    result.error_message = skCrypt("Password meets all requirements").decrypt();
    return result;
}

int main() {
    std::cout << "=== Password Validation Test ===" << std::endl;
    
    // Test cases
    std::string test_passwords[] = {
        "short",           // Too short
        "toolongpassword123!", // Too long
        "NoSpecial123",    // No special character
        "NoNumber!",       // No number
        "123456!@",        // No letter
        "Valid123!",       // Valid password
        "Test@123",        // Valid password
        "Pass\"word1!",    // Contains forbidden character
        "MyPass[123]!",    // Contains forbidden characters
        "Secure#9"         // Valid password
    };
    
    for (const std::string& password : test_passwords) {
        PasswordValidationResult result = validatePassword(password);
        std::cout << "Password: '" << password << "' -> " 
                  << (result.is_valid ? "VALID" : "INVALID") 
                  << " (" << result.error_message << ")" << std::endl;
    }
    
    return 0;
}
